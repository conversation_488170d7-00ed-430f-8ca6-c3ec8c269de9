/**
 * Shared caching service for crypto addresses
 * This service provides a centralized way to cache and retrieve crypto addresses
 * to reduce API calls and improve performance across the application
 */

import axios from 'axios';
import { API_URL } from '../config';

export interface CryptoAddressData {
  [currency: string]: string[] | any;
}

export interface CacheData {
  data: CryptoAddressData;
  timestamp: number;
}

class CryptoAddressCacheService {
  private static instance: CryptoAddressCacheService;
  private readonly CACHE_KEY = 'crypto_addresses_cache';
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

  private constructor() {}

  public static getInstance(): CryptoAddressCacheService {
    if (!CryptoAddressCacheService.instance) {
      CryptoAddressCacheService.instance = new CryptoAddressCacheService();
    }
    return CryptoAddressCacheService.instance;
  }

  /**
   * Get cached addresses from localStorage
   */
  private getCachedData(): CryptoAddressData | null {
    try {
      const cachedData = localStorage.getItem(this.CACHE_KEY);
      if (cachedData) {
        const parsed: CacheData = JSON.parse(cachedData);
        const now = Date.now();
        const isExpired = now - parsed.timestamp > this.CACHE_DURATION;
        
        if (!isExpired && parsed.data) {
          console.log('🔄 Using cached crypto addresses');
          return parsed.data;
        } else {
          console.log('⏰ Cache expired, will fetch fresh data');
          this.clearCache();
        }
      }
    } catch (error) {
      console.warn('Failed to parse cached crypto addresses:', error);
      this.clearCache();
    }
    return null;
  }

  /**
   * Cache addresses to localStorage
   */
  private setCachedData(addresses: CryptoAddressData): void {
    try {
      const cacheData: CacheData = {
        data: addresses,
        timestamp: Date.now()
      };
      localStorage.setItem(this.CACHE_KEY, JSON.stringify(cacheData));
      console.log('💾 Cached crypto addresses for 5 minutes');
    } catch (error) {
      console.warn('Failed to cache crypto addresses:', error);
    }
  }

  /**
   * Clear cache
   */
  public clearCache(): void {
    try {
      localStorage.removeItem(this.CACHE_KEY);
      console.log('🗑️ Cleared crypto addresses cache');
    } catch (error) {
      console.warn('Failed to clear cache:', error);
    }
  }

  /**
   * Fetch fresh data from API and return in the correct format
   */
  private async fetchFromAPI(): Promise<CryptoAddressData> {
    console.log('📡 Fetching fresh crypto addresses from API');

    try {
      const response = await axios.get(`${API_URL}/system/crypto-addresses`, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      console.log('API Response:', response.data);

      if (response.data && response.data.success && response.data.data) {
        // The API returns: { success: true, data: { cryptoAddresses: [...], supportedCurrencies: [...] } }
        if (response.data.data.cryptoAddresses && Array.isArray(response.data.data.cryptoAddresses)) {
          // Return the cryptoAddresses array directly for proper processing
          return response.data.data.cryptoAddresses;
        } else if (response.data.data.cryptoAddresses) {
          // If cryptoAddresses is an object, return it
          return response.data.data.cryptoAddresses;
        } else {
          // Fallback to data object
          return response.data.data;
        }
      } else {
        throw new Error('Invalid API response format');
      }
    } catch (error: any) {
      console.error('Failed to fetch crypto addresses from API:', error);

      // Return fallback data in array format to match API structure
      return [
        {
          currency: 'BTC',
          addresses: [{ address: '**********************************', network: 'bitcoin' }],
          currentIndex: 0,
          enabled: true
        },
        {
          currency: 'ETH',
          addresses: [{ address: '******************************************', network: 'erc20' }],
          currentIndex: 0,
          enabled: true
        },
        {
          currency: 'USDT',
          addresses: [{ address: 'TKQvCcXdgmAQwu4U5RgQJdFfvGbmzXxC9X', network: 'trc20' }],
          currentIndex: 0,
          enabled: true
        },
        {
          currency: 'TRX',
          addresses: [{ address: 'TLPuNinqS5qHuVMHWadqA7RZ2LcxdjCWzb', network: 'tron' }],
          currentIndex: 0,
          enabled: true
        },
        {
          currency: 'DOGE',
          addresses: [{ address: 'DH5yaieqoZN36fDVciNyRueRGvGLR3mr7L', network: 'dogecoin' }],
          currentIndex: 0,
          enabled: true
        }
      ];
    }
  }

  /**
   * Get all crypto addresses (with caching)
   */
  public async getAllAddresses(): Promise<CryptoAddressData> {
    // Check cache first
    let addresses = this.getCachedData();

    // Fetch fresh data if no valid cache
    if (!addresses) {
      addresses = await this.fetchFromAPI();
      this.setCachedData(addresses);
    }

    return addresses;
  }

  /**
   * Get addresses for a specific currency
   */
  public async getAddressesForCurrency(currency: string): Promise<string[]> {
    const allAddresses = await this.getAllAddresses();
    const currencyKey = currency.toUpperCase();

    // Handle array format from API
    if (Array.isArray(allAddresses)) {
      const cryptoAddr = allAddresses.find((addr: any) => addr.currency === currencyKey && addr.enabled);
      if (cryptoAddr && cryptoAddr.addresses) {
        return cryptoAddr.addresses.map((addr: any) =>
          typeof addr === 'object' ? addr.address : addr
        );
      }
    } else if (allAddresses[currencyKey]) {
      // Handle object format
      const addresses = allAddresses[currencyKey];
      return Array.isArray(addresses) ? addresses : [addresses];
    }

    return [];
  }

  /**
   * Get deposit address for a currency (returns first address)
   */
  public async getDepositAddress(currency: string, network?: string): Promise<{
    data: {
      success: boolean;
      data: {
        currency: string;
        network: string;
        addresses: string[];
        address?: string;
      }
    }
  }> {
    const allAddresses = await this.getAllAddresses();
    const currencyKey = currency.toUpperCase();

    // Handle array format from API
    if (Array.isArray(allAddresses)) {
      const cryptoAddr = allAddresses.find((addr: any) => addr.currency === currencyKey && addr.enabled);
      if (cryptoAddr && cryptoAddr.addresses) {
        const currentIndex = cryptoAddr.currentIndex || 0;
        const currentAddressObj = cryptoAddr.addresses[currentIndex] || cryptoAddr.addresses[0];

        const addressString = typeof currentAddressObj === 'object'
          ? currentAddressObj.address
          : currentAddressObj;

        const networkString = typeof currentAddressObj === 'object'
          ? currentAddressObj.network
          : (network || 'mainnet');

        const allAddressStrings = cryptoAddr.addresses.map((addr: any) =>
          typeof addr === 'object' ? addr.address : addr
        );

        return {
          data: {
            success: true,
            data: {
              currency: currencyKey,
              network: networkString,
              addresses: allAddressStrings,
              address: addressString
            }
          }
        };
      }
    } else {
      // Handle legacy object format
      const addresses = await this.getAddressesForCurrency(currency);
      return {
        data: {
          success: true,
          data: {
            currency: currencyKey,
            network: network || 'mainnet',
            addresses: addresses,
            address: addresses[0] || ''
          }
        }
      };
    }

    // Return empty result if currency not found
    return {
      data: {
        success: true,
        data: {
          currency: currencyKey,
          network: network || 'mainnet',
          addresses: [],
          address: ''
        }
      }
    };
  }

  /**
   * Force refresh cache (useful after admin updates)
   */
  public async forceRefresh(): Promise<CryptoAddressData> {
    this.clearCache();
    return await this.getAllAddresses();
  }

  /**
   * Check if cache is valid
   */
  public isCacheValid(): boolean {
    try {
      const cachedData = localStorage.getItem(this.CACHE_KEY);
      if (cachedData) {
        const parsed: CacheData = JSON.parse(cachedData);
        const now = Date.now();
        return (now - parsed.timestamp) < this.CACHE_DURATION;
      }
    } catch (error) {
      return false;
    }
    return false;
  }

  /**
   * Get cache timestamp
   */
  public getCacheTimestamp(): number | null {
    try {
      const cachedData = localStorage.getItem(this.CACHE_KEY);
      if (cachedData) {
        const parsed: CacheData = JSON.parse(cachedData);
        return parsed.timestamp;
      }
    } catch (error) {
      return null;
    }
    return null;
  }
}

// Export singleton instance
export const cryptoAddressCache = CryptoAddressCacheService.getInstance();
export default cryptoAddressCache;
